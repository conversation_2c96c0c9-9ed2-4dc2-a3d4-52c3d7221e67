<?php

namespace Modules\User\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Modules\Core\Models\Concerns\HasSchemalessAttributes;
use Modules\Organization\Traits\UserHasOrganizations;
use Modules\Server\Models\Server;
use Modules\Server\Models\ServerManager;
use Modules\Server\Models\ServerTransfer;
use Modules\Server\Models\SshKey;
use Modules\Team\Models\Team;
use Modules\Team\Traits\UserHasTeams;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, HasRoles, HasSchemalessAttributes, HasUlids, Notifiable, UserHasOrganizations, UserHasTeams;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Modules\User\Database\Factories\UserFactory::new();
    }

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     */
    protected function casts(): array
    {
        return [
            'trial_ends_at' => 'datetime',
            'email_verified_at' => 'datetime',
            'data' => 'object',
        ];
    }

    public function socialAccount(): HasMany
    {
        return $this->hasMany(SocialAccount::class, 'user_id', 'id');
    }

    public function sshKeys(): HasMany
    {
        return $this->hasMany(SshKey::class, 'manager_id', 'id');
    }

    public function servers(): BelongsToMany
    {
        return $this->belongsToMany(Server::class, ServerManager::class, 'user_id', 'server_id')->withTimestamps();
    }

    public function serverTransfers(): HasMany
    {
        return $this->hasMany(ServerTransfer::class, 'manager_id', 'id');
    }

    /**
     * Get all permissions for user in a specific organization
     */
    public function getOrganizationPermissions($organization): array
    {
        $userTeams = $this->getTeamsInOrganization($organization);
        $permissions = [];

        foreach ($userTeams as $teamData) {
            $team = Team::find($teamData['team_id']);
            if ($team) {
                $roles = $this->getRolesInTeam($team);
                foreach ($roles as $role) {
                    foreach ($role->permissions->pluck('name') as $permission) {
                        $permissions[] = $permission;
                    }
                }
            }
        }

        return array_unique($permissions);
    }

    /**
     * Get all role assignments for this user
     */
    public function roleAssignments(): MorphMany
    {
        return $this->morphMany(ModelHasRole::class, 'model', 'model_type', 'model_id');
    }

    /**
     * Get all permission assignments for this user
     */
    public function permissionAssignments(): MorphMany
    {
        return $this->morphMany(ModelHasPermission::class, 'model', 'model_type', 'model_id');
    }

    /**
     * Get active role assignments for this user
     */
    public function activeRoleAssignments(): MorphMany
    {
        return $this->roleAssignments()->where('is_active', true);
    }

    /**
     * Assign a role to a user in a team using Eloquent
     */
    public function assignRoleInTeam(Role $role, Team $team): ModelHasRole
    {
        return $this->roleAssignments()->create([
            'role_id' => $role->id,
            'team_id' => $team->id,
            'is_active' => true,
        ]);
    }

    /**
     * Remove a role from a user in a team using Eloquent
     */
    public function removeRoleInTeam(Role $role, Team $team): bool
    {
        return $this->roleAssignments()
            ->where('role_id', $role->id)
            ->where('team_id', $team->id)
            ->delete() > 0;
    }

    /**
     * Get role assignments for a specific team
     */
    public function getRoleAssignmentsInTeam(Team $team): Collection
    {
        return $this->roleAssignments()
            ->where('team_id', $team->id)
            ->with('role')
            ->get();
    }
}

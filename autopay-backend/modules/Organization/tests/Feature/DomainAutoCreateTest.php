<?php

namespace Modules\Organization\Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\User\Models\User;
use Modules\Organization\Models\Domain;
use Modules\Organization\Models\Organization;
use Tests\TestCase;

class DomainAutoCreateTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Organization $organization;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user
        $this->user = User::factory()->create();

        // Create an organization
        $this->organization = Organization::factory()->create([
            'owner_id' => $this->user->id,
            'name' => 'Test Organization',
        ]);
    }

    /** @test */
    public function it_auto_creates_domain_when_accessing_organization_domain_index(): void
    {
        // Ensure no domain exists initially
        $this->assertDatabaseMissing('domains', [
            'organization_id' => $this->organization->id,
        ]);

        // Make request to get organization domain
        $response = $this->actingAs($this->user)
            ->withoutMiddleware()
            ->getJson("/{$this->organization->id}/domains");

        // Should succeed and auto-create domain
        $response->assertOk();

        // Verify domain was created
        $this->assertDatabaseHas('domains', [
            'organization_id' => $this->organization->id,
            'status' => 'pending',
            'is_active' => true,
        ]);

        // Verify response contains domain data
        $response->assertJsonStructure([
            'success',
            'data' => [
                'id',
                'organization_id',
                'frontend_hostname',
                'backend_hostname',
                'data',
                'status',
                'is_active',
            ],
        ]);

        // Verify default branding data
        $domain = Domain::where('organization_id', $this->organization->id)->first();
        $this->assertNotNull($domain);
        $this->assertEquals('Test Organization', $domain->data['branding']['name']);
        $this->assertEquals('blue', $domain->data['theme']['name']);
        $this->assertNull($domain->frontend_hostname);
        $this->assertNull($domain->backend_hostname);
    }

    /** @test */
    public function it_auto_creates_domain_when_uploading_file(): void
    {
        // Ensure no domain exists initially
        $this->assertDatabaseMissing('domains', [
            'organization_id' => $this->organization->id,
        ]);

        // Create a fake image file
        $file = \Illuminate\Http\UploadedFile::fake()->image('logo.png', 100, 100);

        // Make request to upload file
        $response = $this->actingAs($this->user)
            ->withoutMiddleware()
            ->postJson("/{$this->organization->id}/domains/upload", [
                'file' => $file,
                'type' => 'logo',
            ]);

        // Should succeed and auto-create domain
        $response->assertOk();

        // Verify domain was created
        $this->assertDatabaseHas('domains', [
            'organization_id' => $this->organization->id,
            'status' => 'pending',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_returns_existing_domain_if_already_exists(): void
    {
        // Create a domain first
        $existingDomain = Domain::create([
            'organization_id' => $this->organization->id,
            'frontend_hostname' => 'existing.example.com',
            'backend_hostname' => 'api.existing.example.com',
            'data' => [
                'branding' => ['name' => 'Existing Brand'],
                'theme' => ['name' => 'red'],
            ],
            'status' => 'active',
            'is_active' => true,
        ]);

        // Make request to get organization domain
        $response = $this->actingAs($this->user)
            ->withoutMiddleware()
            ->getJson("/{$this->organization->id}/domains");

        // Should return existing domain
        $response->assertOk();
        $response->assertJson([
            'data' => [
                'id' => $existingDomain->id,
                'frontend_hostname' => 'existing.example.com',
                'backend_hostname' => 'api.existing.example.com',
            ],
        ]);

        // Verify only one domain exists
        $this->assertEquals(1, Domain::where('organization_id', $this->organization->id)->count());
    }
}

<?php

namespace Modules\Organization\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Modules\Core\Helpers\ResponseHelper;
use Modules\Organization\Http\Requests\SetupDomainRequest;
use Modules\Organization\Http\Requests\StoreDomainRequest;
use Modules\Organization\Http\Requests\UpdateDomainRequest;
use Modules\Organization\Models\Domain;
use Modules\Organization\Traits\OrganizationFromRequest;
use Symfony\Component\HttpFoundation\Response;

class DomainController extends Controller
{
    use OrganizationFromRequest;

    /**
     * Get domain configuration by hostname.
     */
    public function getConfig(Request $request): Response
    {
        $hostname = $request->get('hostname', $request->getHost());
        $cacheKey = "domain_config_{$hostname}";

        $config = Cache::remember($cacheKey, 3600, function () use ($hostname) {
            return Domain::where('frontend_hostname', $hostname)->active()->first()?->config;
        });

        if (! $config) {
            return ResponseHelper::error('Không tìm thấy cấu hình domain', null, 404);
        }

        return ResponseHelper::success(data: $config);
    }

    /**
     * Get the domain for the organization (since each org has only one domain).
     * If no domain exists, automatically create a default one.
     */
    public function index(Request $request): Response
    {
        $organization = $this->getOrganizationFromRequest($request);

        $domain = Domain::byOrganization($organization->id)
            ->active()
            ->first();

        if (! $domain) {
            // Auto-create a default domain for the organization
            $domain = $this->createDefaultDomainForOrganization($organization);
        }

        return ResponseHelper::success(data: $domain->toArray());
    }

    /**
     * Create a new domain.
     */
    public function store(StoreDomainRequest $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            $data = $request->validated();

            // Automatically set organization_id from the route
            $data['organization_id'] = $organization->id;

            // Check if the organization already has a domain (limit 1 per organization)
            $existingDomain = Domain::where('organization_id', $organization->id)->first();
            if ($existingDomain) {
                // Update existing domain instead of creating new one
                $existingDomain->update($data);

                // Clear cache for both frontend and backend hostnames
                if ($existingDomain->frontend_hostname) {
                    $this->clearDomainCache($existingDomain->frontend_hostname);
                }
                if ($existingDomain->backend_hostname) {
                    $this->clearDomainCache($existingDomain->backend_hostname);
                }

                return ResponseHelper::success('Cập nhật domain thành công', $existingDomain->fresh()->toArray());
            }

            // Validate that at least one hostname is provided
            if (empty($data['frontend_hostname']) && empty($data['backend_hostname'])) {
                return ResponseHelper::error('Vui lòng nhập ít nhất một trong Frontend Domain hoặc Backend Domain', null, 422);
            }

            // Set default status
            $data['status'] = 'active';
            $data['is_active'] = true;

            $domain = Domain::create($data);

            return ResponseHelper::success('Tạo domain thành công', $domain, 201);
        } catch (ValidationException $e) {
            return ResponseHelper::error('Xác thực thất bại', $e->errors(), 422);
        } catch (Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Update a domain.
     */
    public function update(UpdateDomainRequest $request, $organization, string $id): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            $domain = Domain::where('id', $id)
                ->where('organization_id', $organization->id)
                ->first();

            if (! $domain) {
                return ResponseHelper::error('Không tìm thấy domain', null, 404);
            }

            $data = $request->validated();

            // No additional validation needed for hostname fields

            $domain->update($data);

            // Clear cache for both frontend and backend hostnames
            if ($domain->frontend_hostname) {
                $this->clearDomainCache($domain->frontend_hostname);
            }
            if ($domain->backend_hostname) {
                $this->clearDomainCache($domain->backend_hostname);
            }

            return ResponseHelper::success('Cập nhật domain thành công', $domain->fresh()->toArray());
        } catch (ValidationException $e) {
            return ResponseHelper::error('Xác thực thất bại', $e->errors(), 422);
        } catch (Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Delete a domain.
     */
    public function destroy(string $id, Request $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            $domain = Domain::where('id', $id)
                ->where('organization_id', $organization->id)
                ->first();

            if (! $domain) {
                return ResponseHelper::error('Không tìm thấy domain', null, 404);
            }

            // Clear cache before deleting
            if ($domain->frontend_hostname) {
                $this->clearDomainCache($domain->frontend_hostname);
            }
            if ($domain->backend_hostname) {
                $this->clearDomainCache($domain->backend_hostname);
            }

            $domain->delete();

            return ResponseHelper::success('Xóa domain thành công');
        } catch (Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Setup domain for organization.
     */
    public function setupDomain(SetupDomainRequest $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            // Validate domain format
            $errors = $this->validateDomainFormat($request->hostname);
            if (! empty($errors)) {
                return ResponseHelper::error('Xác thực domain thất bại', $errors, 422);
            }

            $domain = $this->setupDomainForOrganization(
                $request->hostname,
                $organization->id,
                $request->only(['name', 'description'])
            );

            // Clear cache for the new domain
            if ($domain->frontend_hostname) {
                $this->clearDomainCache($domain->frontend_hostname);
            }
            if ($domain->backend_hostname) {
                $this->clearDomainCache($domain->backend_hostname);
            }

            return ResponseHelper::success('Khởi tạo thiết lập domain thành công', $domain, 201);
        } catch (Exception $e) {
            return ResponseHelper::error($e->getMessage(), null, 400);
        }
    }

    /**
     * Check if the hostname is available.
     */
    private function isHostnameAvailable(string $hostname, ?string $excludeId = null): bool
    {
        $query = Domain::where('hostname', $hostname);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return ! $query->exists();
    }

    /**
     * Clear domain cache.
     */
    private function clearDomainCache(string $hostname): void
    {
        Cache::forget("domain_config_{$hostname}");
    }

    /**
     * Validate domain format.
     */
    private function validateDomainFormat(string $hostname): array
    {
        $errors = [];

        // Basic hostname validation
        if (! filter_var($hostname, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME)) {
            $errors[] = 'Định dạng tên miền không hợp lệ';
        }

        // Check for reserved domains
        $reservedDomains = ['localhost', 'autopay.vn', 'www.autopay.vn'];
        if (in_array($hostname, $reservedDomains)) {
            $errors[] = 'Tên miền này đã được bảo lưu và không thể sử dụng';
        }

        return $errors;
    }

    /**
     * Create a default domain for organization when none exists.
     */
    private function createDefaultDomainForOrganization($organization): Domain
    {
        // Create default domain data with organization branding
        $defaultData = [
            'branding' => [
                'name' => $organization->name ?? 'AutoPAY',
                'slogan' => null,
                'email' => null,
                'phone' => null,
                'logo_url' => null,
                'favicon_url' => null,
            ],
            'theme' => [
                'name' => 'blue',
            ],
            'seo' => [
                'title' => $organization->name ?? 'AutoPAY',
                'description' => null,
                'keywords' => null,
                'og_image' => null,
            ],
            'contact' => [],
        ];

        // Create domain record with default values
        $domain = Domain::create([
            'organization_id' => $organization->id,
            'frontend_hostname' => null, // Will be set later when user configures
            'backend_hostname' => null,  // Will be set later when user configures
            'data' => $defaultData,
            'status' => 'pending', // Pending until user configures hostnames
            'is_active' => true,
        ]);

        return $domain;
    }

    /**
     * Setup domain for organization.
     */
    private function setupDomainForOrganization(string $hostname, string $organizationId, array $additionalData = []): Domain
    {
        // Check if the organization already has a domain
        $existingDomain = Domain::where('organization_id', $organizationId)->first();

        // Determine domain type
        $appDomain = config('app.domain', 'autopay.vn');
        $domainType = str_ends_with($hostname, ".{$appDomain}") ? 'subdomain' : 'custom';

        // Prepare domain data
        $domainData = array_merge([
            'organization_id' => $organizationId,
            'frontend_hostname' => $hostname, // Use frontend_hostname instead of hostname
            'backend_hostname' => null, // Can be set later
            'status' => $domainType === 'subdomain' ? 'active' : 'pending',
            'is_active' => true,
        ], $additionalData);

        if ($existingDomain) {
            // Update existing domain
            $existingDomain->update($domainData);
            return $existingDomain->fresh();
        }

        // Create new domain
        return Domain::create($domainData);
    }

    /**
     * Upload file for domain branding (logo, favicon, etc.)
     */
    public function uploadFile(Request $request): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            // Validate file upload and type
            $request->validate([
                'file' => [
                    'required',
                    'file',
                    'image',
                    'mimes:jpeg,jpg,png,gif,webp,svg',
                    'max:5120', // 5MB in kilobytes
                ],
                'type' => [
                    'required',
                    'string',
                    'in:logo,favicon',
                ],
            ], [
                'file.required' => 'Vui lòng chọn file để upload.',
                'file.file' => 'File upload không hợp lệ.',
                'file.image' => 'File phải là hình ảnh.',
                'file.mimes' => 'File phải có định dạng: jpeg, jpg, png, gif, webp, svg.',
                'file.max' => 'Kích thước file không được vượt quá 5MB.',
                'type.required' => 'Vui lòng chỉ định loại file (logo hoặc favicon).',
                'type.in' => 'Loại file phải là logo hoặc favicon.',
            ]);

            $file = $request->file('file');
            $type = $request->input('type'); // 'logo' or 'favicon'

            // Get organization's domain, create default if not exists
            $domain = Domain::where('organization_id', $organization->id)->first();
            if (!$domain) {
                $domain = $this->createDefaultDomainForOrganization($organization);
            }

            // Get current file URL to delete old file
            $currentData = $domain->data ?? [];
            $brandingData = $currentData['branding'] ?? [];
            $fieldName = $type . '_url';
            $currentUrl = $brandingData[$fieldName] ?? null;

            // Delete old file if exists
            if ($currentUrl) {
                $this->deleteOldFile($currentUrl);
            }

            // Generate unique filename with organization prefix and type
            $filename = $organization->id . '_' . $type . '_' . Str::uuid() . '.' . $file->getClientOriginalExtension();

            // Create image manager instance
            $manager = new ImageManager(new Driver());

            // Read and resize image
            $image = $manager->read($file->getPathname());

            // Resize image to max height of 50px while maintaining aspect ratio
            $image->scaleDown(height: 50);

            // Get the storage path
            $storagePath = storage_path('app/public/uploads/organizations/' . $filename);

            // Ensure directory exists
            $directory = dirname($storagePath);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            // Save resized image
            $image->save($storagePath);

            // Generate public URL
            $path = 'uploads/organizations/' . $filename;
            $url = Storage::disk('public')->url($path);

            // Update domain data with new URL
            $updatedBrandingData = $brandingData;
            $updatedBrandingData[$fieldName] = $url;

            $updatedData = $currentData;
            $updatedData['branding'] = $updatedBrandingData;

            $domain->update(['data' => $updatedData]);

            // Clear cache for both frontend and backend hostnames
            if ($domain->frontend_hostname) {
                $this->clearDomainCache($domain->frontend_hostname);
            }
            if ($domain->backend_hostname) {
                $this->clearDomainCache($domain->backend_hostname);
            }

            return ResponseHelper::success('File uploaded successfully', [
                'url' => $url,
                'path' => $path,
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'type' => $type,
            ]);

        } catch (ValidationException $e) {
            return ResponseHelper::error('Xác thực thất bại', $e->errors(), 422);
        } catch (Exception $e) {
            return ResponseHelper::error('Upload thất bại: ' . $e->getMessage(), null, 500);
        }
    }

    /**
     * Delete file from storage (public endpoint)
     */
    public function deleteFile(Request $request, $organization): Response
    {
        try {
            $organization = $this->getOrganizationFromRequest($request);

            $request->validate([
                'url' => 'required|string',
                'type' => 'required|string|in:logo,favicon',
            ]);

            $url = $request->input('url');
            $type = $request->input('type');

            // Extract filename from URL
            $urlParts = parse_url($url);
            $path = $urlParts['path'] ?? '';
            $filename = basename($path);

            // Verify the file belongs to this organization
            if (!str_starts_with($filename, $organization->id . '_' . $type . '_')) {
                return ResponseHelper::error('Unauthorized file access', null, 403);
            }

            // Delete file using existing private method
            $this->deleteOldFile($url);

            return ResponseHelper::success('File deleted successfully', [
                'filename' => $filename,
                'type' => $type,
            ]);
        } catch (ValidationException $e) {
            return ResponseHelper::error('Validation failed', $e->errors(), 422);
        } catch (Exception $e) {
            return ResponseHelper::error('File deletion failed', null, 500);
        }
    }

    /**
     * Delete old file from storage
     */
    private function deleteOldFile(string $url): void
    {
        try {
            // Extract path from URL
            // URL format: http://domain.com/storage/uploads/organizations/filename.ext
            $urlParts = parse_url($url);
            $path = $urlParts['path'] ?? '';

            // Remove /storage/ prefix to get the actual storage path
            $storagePath = str_replace('/storage/', '', $path);

            // Delete file if exists
            if (Storage::disk('public')->exists($storagePath)) {
                Storage::disk('public')->delete($storagePath);
            }
        } catch (Exception $e) {
            // Log error but don't fail the upload process
        }
    }
}

<?php

namespace Modules\Organization\Traits;

use Illuminate\Http\Request;
use Modules\Organization\Models\Organization;

trait OrganizationFromRequest
{
    /**
     * Get organization from request (from route binding)
     */
    protected function getOrganizationFromRequest(Request $request): ?Organization
    {
        $organization = $request->route('organization');

        // If organization is already an instance, return it
        if ($organization instanceof Organization) {
            return $organization;
        }

        // Fallback for testing: if organization is an ID string, find the model
        if (is_string($organization)) {
            return Organization::find($organization);
        }

        return null;
    }

    /**
     * Get organization alias from request
     */
    protected function getOrganizationAliasFromRequest(Request $request): ?string
    {
        return $this->getOrganizationFromRequest($request)?->alias;
    }

    /**
     * Get organization ID from request
     */
    protected function getOrganizationIdFromRequest(Request $request): ?string
    {
        return $this->getOrganizationFromRequest($request)?->id;
    }
}
